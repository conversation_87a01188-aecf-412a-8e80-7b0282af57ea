const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function createEssayDataset() {
  try {
    // Read the CSV file
    const csvPath = path.join(__dirname, '../data/dataset_essay.csv');
    const csvContent = await fs.readFile(csvPath, 'utf8');

    // Create dataset entry
    const datasetToInsert = {
      name: 'Essay Evaluation Dataset',
      description: 'Dataset for essay evaluation with competency analysis across Innovation, Accountability, Leadership, and Problem Solving',
      data: {
        csvData: csvContent,
        type: 'essay_evaluation'
      },
      label: 'essay',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const { data: newDataset, error: insertError } = await supabase
      .from('datasets')
      .insert([datasetToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting Essay dataset:', insertError);
      return;
    }

    console.log('Essay dataset created successfully:', newDataset.id);

  } catch (error) {
    console.error('Error creating Essay dataset:', error);
  }
}

createEssayDataset();
